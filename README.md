# Pyrogram and Telethon String Session Bot [@SessionStringzBot](https://t.me/SessionStringZBot)

> A star ⭐ from you means a lot to us!

<p align="center"><a href="https://github.com/EL-Coders/SessionStringBot"><img src="https://telegra.ph/file/7ec22c82f580a334dd13e.jpg" width="2000"></a></p>

Telegram bot to generate pyrogram and telethon string session.

[![Open Source Love svg1](https://badges.frapsoft.com/os/v1/open-source.svg?v=103)](https://github.com/ellerbrock/open-source-badges/)

## Usage


### Local Deploying

1. Clone the repo
   ```markdown
   git clone https://github.com/EL-Coders/SessionStringBot
   ```
2. Set up MongoDB and get connection URL.

3. Enter the directory
   ```markdown
   cd StringSessionBot
   ```
   
4. Rename `.env.sample` to `.env` and fill the needed variables

5. Run the file
   ```markdown
   python3 -m ssnbot
   ```

## Environment Variables

#### Mandatory Vars

- `APP_ID` - Get this from [my.telegram.org](https://my.telegram.org/auth)
- `API_HASH` - Get this from [my.telegram.org](https://my.telegram.org/auth)
- `BOT_TOKEN` - Get this from [@BotFather](https://t.me/BotFather)
- `MONGO_URL` - MongoDB connection string (e.g., mongodb://localhost:27017/sessionbot).
- `MUST_JOIN` - Username/ID of your telegram channel/group.
- `ADMINS` - ID of users which will be able to broadcast.

## Functions

> More features soon if suggested by you :)

## To-Do

> That's on you mainly...

[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com)

## Credits
- [Stark Bots](https://github.com/StarkBotsIndustries) for [StringSessionBot](https://github.com/StarkBotsIndustries/StringSessionBot) repo
- [Dan Tès](https://github.com/delivrance) for his [Pyrogram](https://docs.pyrogram.org) Library
- [Lonami](https://github.com/Lonami) for his [Telethon](https://docs.telethon.dev) Library
- [aylak](https://t.me/ayIak) for **Telethon** idea of [v1.0.0]

## Support

Channel :- [@ELUpdates](https://t.me/ELUpdates)

Group Chat :- [@ELSupport](https://t.me/ELSupport)

## :)

[![ForTheBadge made-with-python](http://ForTheBadge.com/images/badges/made-with-python.svg)](https://www.python.org/)

[![ForTheBadge built-with-love](http://ForTheBadge.com/images/badges/built-with-love.svg)](https://github.com/EL-Coders)

[![ForTheBadge makes-people-smile](http://ForTheBadge.com/images/badges/makes-people-smile.svg)](https://github.com/EL-Coders)
