import threading
import async<PERSON>
from pymongo import MongoClient
from motor.motor_asyncio import AsyncIOMotorClient
from ssnbot import <PERSON><PERSON>GO_URL


# MongoDB client setup
client = AsyncIOMotorClient(MONGO_URL)
db = client.sessionbot
broadcast_collection = db.broadcast

INSERTION_LOCK = threading.RLock()


async def add_user(user_id, user_name):
    with INSERTION_LOCK:
        try:
            existing_user = await broadcast_collection.find_one({"user_id": user_id})
            if not existing_user:
                await broadcast_collection.insert_one({
                    "user_id": user_id,
                    "user_name": user_name
                })
        except Exception as e:
            print(f"Error adding user: {e}")


async def is_user(user_id):
    with INSERTION_LOCK:
        try:
            user = await broadcast_collection.find_one({"user_id": user_id})
            return user["user_id"] if user else False
        except Exception as e:
            print(f"Error checking user: {e}")
            return False


async def query_msg():
    try:
        cursor = broadcast_collection.find({}, {"user_id": 1}).sort("user_id", 1)
        users = []
        async for user in cursor:
            users.append((user["user_id"],))
        return users
    except Exception as e:
        print(f"Error querying users: {e}")
        return []


async def del_user(user_id):
    with INSERTION_LOCK:
        try:
            await broadcast_collection.delete_one({"user_id": user_id})
        except Exception as e:
            print(f"Error deleting user: {e}")
        